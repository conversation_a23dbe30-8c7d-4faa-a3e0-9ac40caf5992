<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗调试测试</title>
    <style>
        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-container {
            width: 800px;
            height: 600px;
            background: #1a2332;
            border: 2px solid #00d4ff;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .modal-header {
            padding: 20px;
            background: #0a0f1c;
            border-bottom: 1px solid #3a4a5c;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .modal-close-btn {
            background: none;
            border: none;
            color: #00d4ff;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .modal-content {
            flex: 1;
            padding: 0;
            position: relative;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #1a2332;
        }

        .test-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #00d4ff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            z-index: 9999;
        }

        .test-btn:hover {
            background: #0099cc;
        }

        body {
            margin: 0;
            padding: 20px;
            background: #0a0f1c;
            color: white;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <h1>弹窗功能调试测试</h1>
    <p>点击右上角的"测试弹窗"按钮来测试弹窗功能。</p>
    
    <button class="test-btn" onclick="testModal()">测试弹窗</button>

    <!-- 功能模块弹窗 -->
    <div id="moduleModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">测试模块</h2>
                <button class="modal-close-btn" onclick="closeModal()" title="关闭">
                    ×
                </button>
            </div>
            <div class="modal-content">
                <iframe id="moduleIframe" class="modal-iframe" src=""></iframe>
            </div>
        </div>
    </div>

    <script>
        function testModal() {
            console.log('=== 测试弹窗功能 ===');
            const modalOverlay = document.getElementById('moduleModal');
            const moduleIframe = document.getElementById('moduleIframe');
            const modalTitle = document.querySelector('.modal-title');
            
            console.log('modalOverlay:', modalOverlay);
            console.log('moduleIframe:', moduleIframe);
            console.log('modalTitle:', modalTitle);
            
            if (modalOverlay) {
                console.log('添加show类之前:', modalOverlay.classList.toString());
                console.log('弹窗样式（显示前）:', window.getComputedStyle(modalOverlay));
                
                modalOverlay.classList.add('show');
                
                console.log('添加show类之后:', modalOverlay.classList.toString());
                console.log('弹窗样式（显示后）:', window.getComputedStyle(modalOverlay));
                
                // 设置测试内容
                if (moduleIframe) {
                    moduleIframe.src = 'data:text/html,<h1 style="color: white; text-align: center; margin-top: 200px;">测试页面</h1><p style="color: white; text-align: center;">弹窗功能正常工作!</p>';
                }
                
                if (modalTitle) {
                    modalTitle.textContent = '测试弹窗标题';
                }
            } else {
                console.error('modalOverlay 元素未找到!');
            }
        }

        function closeModal() {
            console.log('关闭弹窗');
            const modalOverlay = document.getElementById('moduleModal');
            const moduleIframe = document.getElementById('moduleIframe');
            
            if (modalOverlay) {
                modalOverlay.classList.remove('show');
            }
            
            if (moduleIframe) {
                setTimeout(() => {
                    moduleIframe.src = '';
                }, 300);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化');
            
            // 检查弹窗元素是否存在
            const modalOverlay = document.getElementById('moduleModal');
            console.log('弹窗元素检查:', modalOverlay);
            
            if (modalOverlay) {
                console.log('弹窗初始样式:', window.getComputedStyle(modalOverlay));
            }
        });
    </script>
</body>
</html>
