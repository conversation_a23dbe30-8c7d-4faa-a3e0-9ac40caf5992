<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 触摸屏版本</title>
    <link rel="shortcut icon" href="logo.png">
    <!-- 引入 echarts -->
    <script src="./echarts/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入 MQTT 工具 -->
    <script type="module" src="mqttTool.js"></script>
    <!-- 引入电气数据处理器 -->
    <script src="electricalDataProcessor.js"></script>
    <!-- 引入演示脚本 -->
    <script src="demo-script.js"></script>
    <!-- 引入集成测试脚本 -->
    <script src="integration-test.js"></script>
    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /**
         * 桂林智源 SVG 数字化系统 - 触摸屏版本样式
         * 专为1920×1080触摸屏优化设计
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            position: relative;
            user-select: none; /* 触摸屏优化：禁用文本选择 */
            -webkit-touch-callout: none; /* 禁用长按菜单 */
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* 主容器 */
        .touch-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: 120px 540px 1fr;
            grid-template-rows: 80px 1fr;
            grid-template-areas:
                "header header header"
                "sidebar main-content right-panel";
            z-index: 1;
        }

        /* 顶部标题栏 */
        .header {
            grid-area: header;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-image {
            width: 40px;
            height: 40px;
            border-radius: 8px;
        }

        .logo-text {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .system-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-primary);
            text-shadow: 0 2px 10px rgba(0, 212, 255, 0.5);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .login-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .login-btn {
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            cursor: pointer;
            padding: 10px 20px;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }

        .login-btn:hover,
        .login-btn:active {
            border-color: var(--primary-color);
            box-shadow: 0 6px 25px rgba(0, 212, 255, 0.4);
            transform: scale(1.05);
        }

        .time-display {
            font-size: 18px;
            color: var(--accent-color);
            font-weight: bold;
            text-shadow: 0 2px 10px rgba(0, 255, 136, 0.5);
        }

        /* 左侧导航栏 */
        .sidebar {
            grid-area: sidebar;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-right: 2px solid var(--border-color);
            padding: 10px 5px;
            overflow-y: auto;
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        /* 隐藏滚动条 - Webkit 浏览器 (Chrome, Safari) */
        .sidebar::-webkit-scrollbar {
            display: none;
        }

        .nav-menu {
            display: flex;
            flex-direction: column;
            gap: 0;
            background: rgba(26, 35, 50, 0.5);
            border-radius: 8px;
            overflow: hidden;
        }

        .nav-item {
            background: transparent;
            border: none;
            border-bottom: 1px solid rgba(58, 74, 92, 0.5);
            border-radius: 0;
            color: var(--text-primary);
            cursor: pointer;
            padding: 12px 8px;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            touch-action: manipulation;
            min-height: 70px;
            text-align: center;
            position: relative;
        }
        
        /* 移除最后一个导航项的下边框 */
        .nav-item:last-child {
            border-bottom: none;
        }

        .nav-item:hover,
        .nav-item:active {
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.2), transparent);
            border-left: 3px solid var(--primary-color);
            box-shadow: inset 0 0 15px rgba(0, 212, 255, 0.2);
            transform: none;
        }
        
        /* 添加活动状态样式 */
        .nav-item.active {
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.3), transparent);
            border-left: 3px solid var(--primary-color);
            box-shadow: inset 0 0 15px rgba(0, 212, 255, 0.3);
        }

        .nav-item i {
            font-size: 24px;
            color: var(--primary-color);
            text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);
            margin-bottom: 2px;
        }

        .nav-item span {
            font-size: 12px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        .nav-item.hidden {
            display: none;
        }
        
        .nav-item.hidden-by-permission {
            display: none !important;
        }

        /* 中间区域 */
        .main-content {
            grid-area: main-content;
            display: flex;
            flex-direction: column;
            padding: 15px;
            gap: 15px;
        }

        /* 上部：电气系统状态 */
        .electrical-status {
            flex: 0 0 auto;
            background: rgba(26, 35, 50, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 15px;
        }

        .electrical-status h4 {
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .electrical-status h4 i {
            color: var(--primary-color);
            font-size: 16px;
        }

        /* 中部：网侧负载无功电流图表 */
        .grid-current-chart {
            flex: 1;
            background: rgba(26, 35, 50, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            min-height: 300px;
        }

        .grid-current-chart h4 {
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .grid-current-chart h4 i {
            color: var(--primary-color);
            font-size: 16px;
        }

        /* 下部：电气系统参数 */
        .electrical-params {
            flex: 0 0 auto;
            background: rgba(26, 35, 50, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 15px;
        }

        .electrical-params h4 {
            font-size: 16px;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .electrical-params h4 i {
            color: var(--primary-color);
            font-size: 16px;
        }

        /* 右侧区域 */
        .right-panel {
            grid-area: right-panel;
            background: rgba(26, 35, 50, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            margin: 15px;
        }

        /* 拓扑容器包装器 */
        .topology-wrapper {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        /* 拓扑容器样式 */
        .topology-container {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .topology-container.active {
            opacity: 1;
            visibility: visible;
        }

        .topology-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--bg-primary);
        }

        /* 实时报警监控区域 */
        .alarm-monitor-section {
            background: var(--bg-card);
            border-top: 1px solid var(--border-color);
            height: 280px;
            display: flex;
            flex-direction: column;
        }

        /* 状态网格布局 */
        .status-grid-five {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin-bottom: 0;
        }

        .status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 10px 6px;
            background: rgba(42, 49, 66, 0.7);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            touch-action: manipulation;
            min-height: 65px;
        }

        .status-item:hover,
        .status-item:active {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            transform: scale(1.02);
        }

        .status-item.active {
            border-color: var(--success-color);
            box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-indicator.running {
            background: var(--success-color);
            color: var(--bg-primary);
        }

        .status-indicator.fault {
            background: var(--error-color);
            color: var(--text-primary);
            animation: pulse 1s infinite;
        }

        .status-indicator.ready {
            background: var(--primary-color);
            color: var(--bg-primary);
        }

        .status-indicator.standby {
            background: #9E9E9E;
            color: var(--text-primary);
        }

        .status-indicator.waiting {
            background: var(--warning-color);
            color: var(--bg-primary);
        }

        .status-label {
            font-size: 12px;
            color: var(--text-primary);
            text-align: center;
            font-weight: 500;
            line-height: 1.2;
        }

        /* 参数网格布局 */
        .parameter-grid-compact {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .parameter-item {
            background: rgba(42, 49, 66, 0.7);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            touch-action: manipulation;
            min-height: 60px;
        }

        .parameter-item:hover,
        .parameter-item:active {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            transform: scale(1.02);
        }

        .parameter-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
            height: 100%;
            justify-content: center;
        }

        .parameter-label {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.2;
        }

        .parameter-value {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: bold;
            line-height: 1.2;
        }

        /* 图表容器 */
        .chart-content {
            flex: 1;
            width: 100%;
            min-height: 200px;
        }



        /* 报警监控样式 */
        .alarm-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .alarm-header h4 {
            font-size: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
        }

        .alarm-header i {
            color: var(--primary-color);
            font-size: 16px;
        }

        .alarm-filters {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 4px 12px;
            border: 1px solid var(--border-color);
            border-radius: 15px;
            background: transparent;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--bg-primary);
        }

        .alarm-status {
            display: flex;
            gap: 15px;
            font-size: 12px;
        }

        .data-status {
            color: var(--success-color);
        }

        .last-update {
            color: var(--text-secondary);
        }

        .alarm-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px 20px;
        }

        .alarm-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 10px;
            margin-bottom: 4px;
            background: rgba(42, 49, 66, 0.5);
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
            font-size: 12px;
        }

        .alarm-item.warning {
            border-left-color: var(--warning-color);
        }

        .alarm-item.error {
            border-left-color: var(--error-color);
        }

        .alarm-item.recovery {
            border-left-color: var(--success-color);
        }

        .alarm-sequence {
            font-weight: bold;
            color: var(--text-secondary);
            min-width: 30px;
        }

        .alarm-time {
            color: var(--text-secondary);
            min-width: 130px;
            font-family: monospace;
        }

        .alarm-message {
            color: var(--text-primary);
            flex: 1;
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-container {
            width: 1366px;
            height: 768px;
            background: var(--bg-primary);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
        }

        .modal-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1001;
            
        }

        .modal-header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .modal-title {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .debug-menu-btn {
            background: var(--bg-tertiary);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            color: var(--primary-color);
            cursor: pointer;
            padding: 8px 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }

        .debug-menu-btn:hover,
        .debug-menu-btn:active {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            transform: scale(1.05);
        }

        /* 调试菜单样式 */
        .debug-menu {
            position: absolute;
            top: 80px;
            left: 20px;
            width: 300px;
            max-height: calc(100% - 100px);
            background: var(--bg-secondary);
            border: 2px solid var(--primary-color);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
            z-index: 1002;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }

        .debug-menu.show {
            opacity: 1;
            transform: translateY(0);
        }

        .debug-menu-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }



        .debug-menu-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .debug-menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            margin-bottom: 5px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }

        .debug-menu-item:hover,
        .debug-menu-item:active {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            transform: translateX(5px);
        }

        .debug-menu-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: var(--bg-primary);
        }

        .debug-menu-item i {
            color: var(--primary-color);
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .debug-menu-item.active i {
            color: var(--bg-primary);
        }

        .modal-close-btn {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, rgba(42, 52, 65, 0.9), rgba(26, 35, 50, 0.9));
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            touch-action: manipulation;
        }

        .modal-close-btn:hover,
        .modal-close-btn:active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.2));
            border-color: var(--primary-color);
            color: var(--primary-color);
            box-shadow: 0 6px 25px rgba(0, 212, 255, 0.4);
            transform: scale(1.05);
        }

        .modal-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--bg-primary);
        }

        /* 登录弹窗样式 */
        .login-modal {
            width: 400px;
            max-width: 90vw;
            height: auto;
            min-height: 300px;
            background: var(--bg-secondary);
            border: 2px solid var(--primary-color);
            border-radius: 15px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            box-sizing: border-box;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
            width: 100%;
            box-sizing: border-box;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
        }

        .form-group label {
            font-size: 16px;
            color: var(--text-primary);
            font-weight: bold;
        }

        .form-group input {
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-size: 16px;
            transition: all 0.3s ease;
            width: 100%;
            box-sizing: border-box;
            touch-action: manipulation;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .login-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            width: 100%;
            margin-top: 10px;
            box-sizing: border-box;
        }

        .btn {
            padding: 12px 20px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            touch-action: manipulation;
            flex: 1;
            max-width: 150px;
            box-sizing: border-box;
        }

        .btn.primary {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: var(--bg-primary);
        }

        .btn:hover,
        .btn:active {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        /* 权限配置弹窗样式 */
        .permission-modal {
            width: 500px;
            height: 600px;
            background: var(--bg-secondary);
            border: 2px solid var(--primary-color);
            border-radius: 15px;
            padding: 30px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .permission-list {
            flex: 1;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            min-height: 60px;
            box-sizing: border-box;
        }

        .permission-label {
            font-size: 16px;
            color: var(--text-primary);
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: var(--border-color);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: var(--primary-color);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .toggle-switch.active::after {
            left: 33px;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 响应式优化 */
        @media (max-width: 1920px) {
            body {
                width: 100vw;
                height: 100vh;
            }

            .modal-container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
            }

            .permission-modal {
                width: 90vw;
                max-width: 800px;
                height: auto;
                max-height: 80vh;
            }

            .permission-list {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .permission-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="touch-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="logo.png" alt="桂林智源" class="logo-image">
                    <span class="logo-text">桂林智源</span>
                </div>
                <div class="system-title">SVG 数字化系统</div>
            </div>
            <div class="header-right">
                <div class="login-info">
                    <button class="login-btn" id="loginBtn" onclick="toggleLogin()">
                        <i class="fas fa-user"></i>
                        <span id="loginText">登录</span>
                    </button>
                </div>
                <div class="time-display" id="currentTime"></div>
            </div>
        </header>

        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <nav class="nav-menu">
                <div class="nav-item" onclick="openModule('electrical-topology')">
                    <i class="fas fa-bolt"></i>
                    <span>电气拓扑</span>
                </div>
                <div class="nav-item" onclick="openModule('unit-status')">
                    <i class="fas fa-microchip"></i>
                    <span>单元状态</span>
                </div>
                <div class="nav-item" onclick="openModule('history-event')">
                    <i class="fas fa-calendar-alt"></i>
                    <span>历史事件</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="openModule('parameter-curve')">
                    <i class="fas fa-chart-area"></i>
                    <span>参数曲线</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="openModule('fault-wave')">
                    <i class="fas fa-wave-square"></i>
                    <span>故障录波</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="openModule('dsp')">
                    <i class="fas fa-microchip"></i>
                    <span>DSP参数</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="openModule('io-status')">
                    <i class="fas fa-plug"></i>
                    <span>IO状态</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="openModule('cooling-topology')">
                    <i class="fas fa-tint"></i>
                    <span>水冷系统</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="showDebugMenu('debug1')">
                    <i class="fas fa-cogs"></i>
                    <span>调试参数</span>
                </div>
                <div class="nav-item hidden" id="permissionItem" onclick="openPermissionConfig()">
                    <i class="fas fa-key"></i>
                    <span>权限</span>
                </div>
                <div class="nav-item hidden" id="debug2Item" onclick="showDebugMenu('debug2')">
                    <i class="fas fa-tools"></i>
                    <span>调试参数2</span>
                </div>
                <div class="nav-item hidden-by-permission" onclick="openModule('version-info')">
                    <i class="fas fa-info-circle"></i>
                    <span>版本信息</span>
                </div>
            </nav>
        </aside>

    </div>

    <!-- 功能模块弹窗 -->
    <div id="moduleModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <div class="modal-header-left">
                    <button id="debugMenuButton" class="debug-menu-btn" onclick="toggleDebugMenu()" title="显示菜单" style="display: none;">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
                <button class="modal-close-btn" onclick="closeModal()" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <iframe id="moduleIframe" class="modal-iframe" src=""></iframe>
                <!-- 调试菜单 -->
                <div id="debugMenu" class="debug-menu" style="display: none;">
                    <div class="debug-menu-content">
                        <div class="debug-menu-list" id="debugMenuList">
                            <!-- 菜单项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="modal-overlay">
        <div class="login-modal">
            <h3 style="text-align: center; color: var(--primary-color); margin-bottom: 20px;">
                <i class="fas fa-user-lock"></i> 用户登录
            </h3>
            <form class="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" value="8" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" value="262143" required>
                </div>
                <div class="login-buttons">
                    <button type="button" class="btn" onclick="closeLoginModal()">取消</button>
                    <button type="submit" class="btn primary">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 权限配置弹窗 -->
    <div id="permissionModal" class="modal-overlay">
        <div class="permission-modal">
            <h3 style="text-align: center; color: var(--primary-color); margin-bottom: 20px;">
                <i class="fas fa-key"></i> 权限配置
            </h3>
            <div class="permission-list">
                <div class="permission-item">
                    <span class="permission-label">版本信息</span>
                    <div class="toggle-switch active" data-permission="version-info"></div>
                </div>
                <div class="permission-item">
                    <span class="permission-label">参数曲线</span>
                    <div class="toggle-switch active" data-permission="parameter-curve"></div>
                </div>
                <div class="permission-item">
                    <span class="permission-label">故障录波</span>
                    <div class="toggle-switch active" data-permission="fault-wave"></div>
                </div>
                <div class="permission-item">
                    <span class="permission-label">DSP参数</span>
                    <div class="toggle-switch active" data-permission="dsp"></div>
                </div>
                <div class="permission-item">
                    <span class="permission-label">IO状态</span>
                    <div class="toggle-switch active" data-permission="io-status"></div>
                </div>
                <div class="permission-item">
                    <span class="permission-label">水冷系统</span>
                    <div class="toggle-switch active" data-permission="cooling-topology"></div>
                </div>
                <div class="permission-item">
                    <span class="permission-label">调试参数</span>
                    <div class="toggle-switch active" data-permission="debug1"></div>
                </div>
            </div>
            <div class="login-buttons">
                <button type="button" class="btn" onclick="closePermissionModal()">取消</button>
                <button type="button" class="btn primary" onclick="savePermissions()">保存</button>
            </div>
        </div>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 触摸屏版本 JavaScript
         * 功能模块管理、登录控制和权限管理
         */

        // 全局变量
        let isLoggedIn = false;
        let currentUser = null;
        let currentDebugType = null;

        // 调试页面配置
        const debugPages = {
            'debug1': [
                { name: '设备操作', file: '设备操作.html' },
                { name: '系统参数', file: '系统参数.html' },
                { name: '控制模式', file: '控制模式.html' },
                { name: '滤波控制', file: '滤波控制.html' },
                { name: '保护参数', file: '保护参数.html' },
                { name: '保护使能', file: '保护使能.html' }
            ],
            'debug2': [
                { name: '设备操作', file: '设备操作.html' },
                { name: '系统参数', file: '系统参数.html' },
                { name: '控制模式', file: '控制模式.html' },
                { name: '控制参数一', file: '控制参数一.html' },
                { name: '控制参数二', file: '控制参数二.html' },
                { name: '谐波控制', file: '谐波控制.html' },
                { name: '旁路控制', file: '旁路控制.html' }
            ]
        };
        let permissions = {
            'version-info': false,
            'parameter-curve': false,
            'fault-wave': false,
            'dsp': false,
            'io-status': false,
            'cooling-topology': false,
            'debug1': false
        };

        // 模块配置映射
        const moduleConfig = {
            'electrical-topology': {
                title: '电气系统拓扑图',
                icon: 'fas fa-bolt',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date='
            },
            'cooling-topology': {
                title: '水冷系统拓扑图',
                icon: 'fas fa-tint',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date='
            },
            'io-status': {
                title: 'I/O状态监控',
                icon: 'fas fa-plug',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date='
            },
            'unit-status': {
                title: '单元状态监控',
                icon: 'fas fa-microchip',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bc305d60-29d2-4635-82bb-ead9b337b31d&type=3&date='
            },
            'history-event': {
                title: '历史事件',
                icon: 'fas fa-calendar-alt',
                url: '历史事件.html'
            },
            'parameter-curve': {
                title: '参数曲线',
                icon: 'fas fa-chart-area',
                url: '参数曲线.html'
            },
            'dsp': {
                title: 'DSP参数',
                icon: 'fas fa-microchip',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date='
            },
            'fault-wave': {
                title: '故障录波',
                icon: 'fas fa-wave-square',
                url: '故障录波.html'
            },
            'version-info': {
                title: '版本信息',
                icon: 'fas fa-info-circle',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=eb900ac1-737c-4610-b4b3-ea05239531e3&type=3&date='
            }
        };

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });

            const timeDisplay = document.getElementById('currentTime');
            if (timeDisplay) {
                timeDisplay.textContent = timeString;
            }
        }

        /**
         * 登录/注销切换
         */
        function toggleLogin() {
            if (isLoggedIn) {
                // 注销登录
                logout();
            } else {
                // 显示登录弹窗
                showLoginModal();
            }
        }

        /**
         * 显示登录弹窗
         */
        function showLoginModal() {
            const loginModal = document.getElementById('loginModal');
            loginModal.classList.add('show');

            // 聚焦到用户名输入框
            setTimeout(() => {
                document.getElementById('username').focus();
            }, 300);
        }

        /**
         * 关闭登录弹窗
         */
        function closeLoginModal() {
            const loginModal = document.getElementById('loginModal');
            loginModal.classList.remove('show');
        }

        /**
         * 处理登录
         */
        function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            console.log('登录尝试:', { username, password }); // 调试信息

            // 验证登录信息
            if (username === '8' && password === '262143') {
                // 登录成功
                isLoggedIn = true;
                currentUser = username;

                console.log('登录成功'); // 调试信息

                // 更新UI
                updateLoginUI();

                // 显示登录后可见的菜单项
                showLoggedInMenuItems();

                // 关闭登录弹窗
                closeLoginModal();

                // 显示成功消息
                showMessage('登录成功！', 'success');
            } else {
                // 登录失败
                console.log('登录失败: 用户名或密码错误'); // 调试信息
                showMessage('用户名或密码错误！请输入正确的用户名(8)和密码(262143)', 'error');

                // 清空密码输入框
                document.getElementById('password').value = '';

                // 聚焦到用户名输入框
                document.getElementById('username').focus();
            }
        }

        /**
         * 注销登录
         */
        function logout() {
            isLoggedIn = false;
            currentUser = null;

            // 更新UI
            updateLoginUI();

            // 隐藏登录后可见的菜单项
            hideLoggedInMenuItems();

            showMessage('已注销登录', 'info');
        }

        /**
         * 更新登录UI
         */
        function updateLoginUI() {
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');

            if (isLoggedIn) {
                loginText.textContent = '注销';
                loginBtn.style.background = 'linear-gradient(135deg, var(--success-color), var(--accent-color))';
            } else {
                loginText.textContent = '登录';
                loginBtn.style.background = 'linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary))';
            }
        }

        /**
         * 显示登录后可见的菜单项
         */
        function showLoggedInMenuItems() {
            const permissionItem = document.getElementById('permissionItem');
            const debug2Item = document.getElementById('debug2Item');

            if (permissionItem) {
                permissionItem.classList.remove('hidden');
                console.log('显示权限菜单项');
            }

            if (debug2Item) {
                debug2Item.classList.remove('hidden');
                console.log('显示调试参数2菜单项');
            }
        }

        /**
         * 隐藏登录后可见的菜单项
         */
        function hideLoggedInMenuItems() {
            const permissionItem = document.getElementById('permissionItem');
            const debug2Item = document.getElementById('debug2Item');

            if (permissionItem) {
                permissionItem.classList.add('hidden');
                console.log('隐藏权限菜单项');
            }

            if (debug2Item) {
                debug2Item.classList.add('hidden');
                console.log('隐藏调试参数2菜单项');
            }
        }

        /**
         * 显示消息提示
         */
        function showMessage(message, type = 'info') {
            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = `message-toast ${type}`;
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed;
                top: 150px;
                right: 30px;
                padding: 15px 25px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 2000;
                opacity: 0;
                transform: translateX(100px);
                transition: all 0.3s ease;
            `;

            // 设置背景色
            switch (type) {
                case 'success':
                    messageEl.style.background = 'var(--success-color)';
                    break;
                case 'error':
                    messageEl.style.background = 'var(--error-color)';
                    break;
                case 'warning':
                    messageEl.style.background = 'var(--warning-color)';
                    break;
                default:
                    messageEl.style.background = 'var(--primary-color)';
            }

            document.body.appendChild(messageEl);

            // 显示动画
            setTimeout(() => {
                messageEl.style.opacity = '1';
                messageEl.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                messageEl.style.opacity = '0';
                messageEl.style.transform = 'translateX(100px)';
                setTimeout(() => {
                    document.body.removeChild(messageEl);
                }, 300);
            }, 3000);
        }

        /**
         * 打开功能模块
         */
        function openModule(moduleId) {
            console.log(`打开模块: ${moduleId}`);

            // 检查权限（电气拓扑、单元状态、历史事件这三个菜单项不受权限控制）
            if (!['electrical-topology', 'unit-status', 'history-event'].includes(moduleId) && !permissions[moduleId]) {
                showMessage('您没有访问此模块的权限', 'warning');
                return;
            }

            const config = moduleConfig[moduleId];
            if (!config) {
                console.error(`未找到模块配置: ${moduleId}`);
                showMessage('模块配置错误', 'error');
                return;
            }

            // 获取弹窗元素
            const moduleIframe = document.getElementById('moduleIframe');
            const modalOverlay = document.getElementById('moduleModal');
            const menuButton = document.getElementById('debugMenuButton');
            const modalTitle = document.querySelector('#moduleModal .modal-title');

            // 构建URL（如果需要添加时间戳）
            let url = config.url;
            if (url.includes('mqtt.qizhiyun.cc')) {
                url += new Date().getTime();
            }

            // 设置标题和隐藏菜单按钮（非调试模块）
            if (modalTitle) {
                modalTitle.textContent = config.title || '';
            }

            if (menuButton) {
                menuButton.style.display = 'none';
            }

            // 重置当前调试类型
            currentDebugType = null;

            // 设置iframe源
            moduleIframe.src = url;

            // 显示弹窗
            modalOverlay.classList.add('show');

            // 添加触摸屏优化的事件监听
            addTouchOptimization();

            console.log(`已打开模块: ${config.title}`);
        }

        /**
         * 关闭弹窗
         */
        function closeModal() {
            const modalOverlay = document.getElementById('moduleModal');
            const moduleIframe = document.getElementById('moduleIframe');
            const menuButton = document.getElementById('debugMenuButton');
            const modalTitle = document.querySelector('#moduleModal .modal-title');

            modalOverlay.classList.remove('show');

            // 隐藏调试菜单和菜单按钮
            hideDebugMenu();
            if (menuButton) {
                menuButton.style.display = 'none';
            }

            // 清空标题
            if (modalTitle) {
                modalTitle.textContent = '';
            }

            // 重置当前调试类型
            currentDebugType = null;

            // 清空iframe源以释放资源
            setTimeout(() => {
                moduleIframe.src = '';
            }, 300);
        }

        /**
         * 显示调试菜单
         */
        function showDebugMenu(debugType) {
            if (!isLoggedIn && debugType === 'debug2') {
                showMessage('请先登录以访问此功能', 'warning');
                return;
            }

            // 设置当前调试类型
            currentDebugType = debugType;

            // 根据调试类型打开对应页面
            const debugUrl = debugType === 'debug1' ? 'debug1/设备操作.html' : 'debug2/设备操作.html';

            const moduleIframe = document.getElementById('moduleIframe');
            const modalOverlay = document.getElementById('moduleModal');
            const modalTitle = document.querySelector('#moduleModal .modal-title');
            const menuButton = document.getElementById('debugMenuButton');

            // 设置标题和显示菜单按钮
            if (modalTitle) {
                modalTitle.textContent = debugType === 'debug1' ? '调试参数' : '调试参数2';
            }

            if (menuButton) {
                menuButton.style.display = 'block';
            }

            moduleIframe.src = debugUrl;
            modalOverlay.classList.add('show');
        }

        /**
         * 切换调试菜单显示
         */
        function toggleDebugMenu() {
            const debugMenu = document.getElementById('debugMenu');
            if (debugMenu.classList.contains('show')) {
                hideDebugMenu();
            } else {
                showDebugMenuList();
            }
        }

        /**
         * 显示调试菜单
         */
        function showDebugMenuList() {
            if (!currentDebugType) return;

            const debugMenu = document.getElementById('debugMenu');
            const debugMenuList = document.getElementById('debugMenuList');

            // 清空现有菜单项
            debugMenuList.innerHTML = '';

            // 获取当前调试类型的页面列表
            const pages = debugPages[currentDebugType] || [];

            // 生成菜单项
            pages.forEach((page, index) => {
                const menuItem = document.createElement('div');
                menuItem.className = 'debug-menu-item';
                menuItem.innerHTML = `
                    <i class="fas fa-file-alt"></i>
                    <span>${page.name}</span>
                `;

                // 添加点击事件
                menuItem.addEventListener('click', () => {
                    navigateToDebugPage(page.file);
                    hideDebugMenu();
                });

                debugMenuList.appendChild(menuItem);
            });

            // 显示菜单
            debugMenu.style.display = 'block';
            setTimeout(() => {
                debugMenu.classList.add('show');
            }, 10);
        }

        /**
         * 隐藏调试菜单
         */
        function hideDebugMenu() {
            const debugMenu = document.getElementById('debugMenu');
            debugMenu.classList.remove('show');
            setTimeout(() => {
                debugMenu.style.display = 'none';
            }, 300);
        }

        /**
         * 导航到指定的调试页面
         */
        function navigateToDebugPage(fileName) {
            if (!currentDebugType) return;

            const debugUrl = `${currentDebugType}/${fileName}`;
            const moduleIframe = document.getElementById('moduleIframe');

            console.log('导航到调试页面:', debugUrl);
            moduleIframe.src = debugUrl;

            // 更新菜单项的激活状态
            updateDebugMenuActiveState(fileName);
        }

        /**
         * 更新调试菜单项的激活状态
         */
        function updateDebugMenuActiveState(activeFileName) {
            const menuItems = document.querySelectorAll('.debug-menu-item');
            menuItems.forEach(item => {
                const span = item.querySelector('span');
                if (span) {
                    const pages = debugPages[currentDebugType] || [];
                    const page = pages.find(p => p.name === span.textContent);
                    if (page && page.file === activeFileName) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                }
            });
        }

        /**
         * 打开权限配置
         */
        function openPermissionConfig() {
            if (!isLoggedIn) {
                showMessage('请先登录以访问权限配置', 'warning');
                return;
            }

            const permissionModal = document.getElementById('permissionModal');
            permissionModal.classList.add('show');

            // 更新权限开关状态
            updatePermissionSwitches();
        }

        /**
         * 关闭权限配置弹窗
         */
        function closePermissionModal() {
            const permissionModal = document.getElementById('permissionModal');
            permissionModal.classList.remove('show');
        }

        /**
         * 更新权限开关状态
         */
        function updatePermissionSwitches() {
            const switches = document.querySelectorAll('.toggle-switch');
            switches.forEach(switchEl => {
                const permission = switchEl.getAttribute('data-permission');
                if (permissions[permission]) {
                    switchEl.classList.add('active');
                } else {
                    switchEl.classList.remove('active');
                }
            });
        }

        /**
         * 保存权限设置
         */
        function savePermissions() {
            const switches = document.querySelectorAll('.toggle-switch');
            switches.forEach(switchEl => {
                const permission = switchEl.getAttribute('data-permission');
                permissions[permission] = switchEl.classList.contains('active');
            });

            // 更新菜单项显示状态
            updateMenuVisibility();

            closePermissionModal();
            showMessage('权限设置已保存', 'success');
        }

        /**
         * 更新菜单项显示状态
         */
        function updateMenuVisibility() {
            console.log('=== updateMenuVisibility 函数被调用 ===');
            console.log('当前权限设置:', permissions);
            const navItems = document.querySelectorAll('.nav-item');
            console.log('找到', navItems.length, '个导航项');
            
            let hiddenCount = 0;
            let shownCount = 0;
            
            navItems.forEach(item => {
                const onclick = item.getAttribute('onclick');
                console.log('处理导航项，onclick属性:', onclick);
                
                let moduleId = null;
                if (onclick && onclick.includes('openModule')) {
                    moduleId = onclick.match(/openModule\('([^']+)'\)/)?.[1];
                } else if (onclick && onclick.includes('showDebugMenu')) {
                    moduleId = onclick.match(/showDebugMenu\('([^']+)'\)/)?.[1];
                }
                
                console.log('提取到模块ID:', moduleId, '权限:', permissions[moduleId]);
                
                // 权限和调试参数2菜单项不受权限配置控制，只受登录状态控制
                const isPermissionItem = item.id === 'permissionItem';
                const isDebug2Item = item.id === 'debug2Item';
                
                if (isPermissionItem || isDebug2Item) {
                    console.log('跳过权限控制，仅由登录状态控制的菜单项:', item.id);
                    return;
                }
                
                // 电气拓扑、单元状态、历史事件这三个菜单项永远显示，不受权限控制
                if (moduleId && !['electrical-topology', 'unit-status', 'history-event'].includes(moduleId) && !permissions[moduleId]) {
                    console.log('隐藏菜单项:', moduleId);
                    item.style.display = 'none';
                    item.classList.add('hidden-by-permission');
                    hiddenCount++;
                } else {
                    console.log('显示菜单项:', moduleId);
                    item.style.display = 'flex';
                    item.classList.remove('hidden-by-permission');
                    shownCount++;
                }
            });
            
            console.log(`菜单项更新完成: 隐藏了 ${hiddenCount} 个，显示了 ${shownCount} 个`);
            
            // 添加一个视觉指示器，显示函数已被调用
            const indicator = document.getElementById('permission-update-indicator');
            if (!indicator) {
                const newIndicator = document.createElement('div');
                newIndicator.id = 'permission-update-indicator';
                newIndicator.style.cssText = 'position: fixed; bottom: 10px; right: 10px; background: red; color: white; padding: 5px; z-index: 9999; font-size: 12px;';
                newIndicator.textContent = '权限更新已执行';
                document.body.appendChild(newIndicator);
                
                // 3秒后移除指示器
                setTimeout(() => {
                    if (newIndicator.parentNode) {
                        newIndicator.parentNode.removeChild(newIndicator);
                    }
                }, 3000);
            }
        }

        /**
         * 显示参数详情（占位函数）
         */
        function showParameterDetails(parameterId) {
            showMessage(`参数详情: ${parameterId}`, 'info');
        }

        /**
         * 添加触摸屏优化
         */
        function addTouchOptimization() {
            // 防止双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // 防止长按选择文本
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
            });
        }

        /**
         * 初始化图表
         */
        function initializeCharts() {
            // 初始化网侧负载无功电流图表
            const chartContainer = document.getElementById('grid-current-chart');
            if (chartContainer && typeof echarts !== 'undefined') {
                const chart = echarts.init(chartContainer);

                const option = {
                    backgroundColor: 'transparent',
                    title: {
                        show: false
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(26, 35, 50, 0.9)',
                        borderColor: '#00d4ff',
                        textStyle: {
                            color: '#ffffff'
                        }
                    },
                    legend: {
                        data: ['A相', 'B相', 'C相'],
                        textStyle: {
                            color: '#b8c5d6'
                        },
                        top: 10
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: [],
                        axisLine: {
                            lineStyle: {
                                color: '#3a4a5c'
                            }
                        },
                        axisLabel: {
                            color: '#b8c5d6'
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: '#3a4a5c'
                            }
                        },
                        axisLabel: {
                            color: '#b8c5d6'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#3a4a5c'
                            }
                        }
                    },
                    series: [
                        {
                            name: 'A相',
                            type: 'line',
                            data: [],
                            smooth: true,
                            lineStyle: {
                                color: '#00d4ff'
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(0, 212, 255, 0.3)'
                                    }, {
                                        offset: 1, color: 'rgba(0, 212, 255, 0.05)'
                                    }]
                                }
                            }
                        },
                        {
                            name: 'B相',
                            type: 'line',
                            data: [],
                            smooth: true,
                            lineStyle: {
                                color: '#00ff88'
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(0, 255, 136, 0.3)'
                                    }, {
                                        offset: 1, color: 'rgba(0, 255, 136, 0.05)'
                                    }]
                                }
                            }
                        },
                        {
                            name: 'C相',
                            type: 'line',
                            data: [],
                            smooth: true,
                            lineStyle: {
                                color: '#ffaa00'
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(255, 170, 0, 0.3)'
                                    }, {
                                        offset: 1, color: 'rgba(255, 170, 0, 0.05)'
                                    }]
                                }
                            }
                        }
                    ]
                };

                chart.setOption(option);

                // 模拟数据更新
                setInterval(() => {
                    const now = new Date();
                    const timeStr = now.toLocaleTimeString();
                    const data = option.xAxis.data;
                    const seriesData = option.series;

                    data.push(timeStr);
                    if (data.length > 20) {
                        data.shift();
                    }

                    seriesData.forEach((series, index) => {
                        const value = Math.random() * 100 + 50 + index * 20;
                        series.data.push(value);
                        if (series.data.length > 20) {
                            series.data.shift();
                        }
                    });

                    chart.setOption(option);
                }, 2000);
            }
        }

        /**
         * 初始化页面
         */
        function initializePage() {
            console.log('初始化触摸屏版本页面');

            // 初始化登录状态
            updateLoginUI();
            hideLoggedInMenuItems(); // 默认隐藏登录后可见的菜单项

            // 确保三个特定菜单项（电气拓扑、单元状态、历史事件）始终显示
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                const onclick = item.getAttribute('onclick');
                if (onclick && onclick.includes('openModule')) {
                    const moduleId = onclick.match(/openModule\('([^']+)'\)/)?.[1];
                    if (moduleId && ['electrical-topology', 'unit-status', 'history-event'].includes(moduleId)) {
                        item.style.display = 'flex';
                    }
                }
            });

            // 更新时间显示
            updateTime();
            setInterval(updateTime, 1000);

            // 初始化图表
            initializeCharts();

            // 添加权限开关点击事件
            const switches = document.querySelectorAll('.toggle-switch');
            switches.forEach(switchEl => {
                switchEl.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            });

            // 添加报警过滤器点击事件
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const filterType = this.getAttribute('data-filter');
                    filterAlarms(filterType);
                });
            });

            // 添加触摸屏优化
            addTouchOptimization();

            // 点击弹窗外部关闭弹窗
            const modalOverlays = document.querySelectorAll('.modal-overlay');
            modalOverlays.forEach(overlay => {
                overlay.addEventListener('click', function(event) {
                    if (event.target === overlay) {
                        if (overlay.id === 'moduleModal') {
                            closeModal();
                        } else if (overlay.id === 'loginModal') {
                            closeLoginModal();
                        } else if (overlay.id === 'permissionModal') {
                            closePermissionModal();
                        }
                    }
                });
            });

            // 键盘事件处理（ESC键关闭弹窗）
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeModal();
                    closeLoginModal();
                    closePermissionModal();
                }
            });

            // 初始化拓扑显示
            initializeTopology();

            // 根据权限设置更新菜单项显示状态
            console.log('立即调用updateMenuVisibility');
            updateMenuVisibility();
            
            // 延迟再次更新菜单项显示状态，确保所有元素都已加载
            setTimeout(() => {
                console.log('延迟调用updateMenuVisibility');
                updateMenuVisibility();
            }, 500);

            console.log('触摸屏版本页面初始化完成');
        }

        /**
         * 初始化拓扑显示
         */
        function initializeTopology() {
            // 默认显示电气拓扑
            const electricalIframe = document.getElementById('electrical-topology-iframe');
            if (electricalIframe) {
                const electricalUrl = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date=' + new Date().getTime();
                electricalIframe.src = electricalUrl;
            }

            // 初始化水冷拓扑
            const coolingIframe = document.getElementById('cooling-topology-iframe');
            if (coolingIframe) {
                const coolingUrl = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date=' + new Date().getTime();
                coolingIframe.src = coolingUrl;
            }
        }

        /**
         * 切换拓扑显示
         */
        function switchTopology(topologyType) {
            const electricalContainer = document.getElementById('electricalTopologyContainer');
            const coolingContainer = document.getElementById('coolingTopologyContainer');

            if (topologyType === 'electrical') {
                electricalContainer.classList.add('active');
                coolingContainer.classList.remove('active');
            } else if (topologyType === 'cooling') {
                electricalContainer.classList.remove('active');
                coolingContainer.classList.add('active');
            }
        }

        /**
         * 模拟报警数据更新
         */
        function updateAlarmData() {
            const alarmList = document.getElementById('alarmList');
            if (!alarmList) return;

            // 模拟新的报警数据
            const alarmTypes = ['', 'warning', 'error', 'recovery'];
            const messages = [
                '系统正常运行',
                '电压波动预警',
                '温度过高报警',
                '通信故障',
                '系统恢复正常',
                '负载异常',
                '设备维护提醒',
                'SVG系统启动',
                '水冷系统正常',
                '功率因数调整'
            ];

            // 随机生成报警项
            if (Math.random() < 0.3) { // 30%概率生成新报警
                const now = new Date();
                const timeStr = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                const alarmItem = document.createElement('div');
                const alarmType = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];
                const message = messages[Math.floor(Math.random() * messages.length)];
                const sequence = String(Date.now()).slice(-3);

                alarmItem.className = `alarm-item ${alarmType}`;
                alarmItem.innerHTML = `
                    <span class="alarm-sequence">${sequence}</span>
                    <span class="alarm-time">${timeStr}</span>
                    <span class="alarm-message">${message}</span>
                `;

                // 插入到列表顶部
                alarmList.insertBefore(alarmItem, alarmList.firstChild);

                // 限制显示数量
                const items = alarmList.querySelectorAll('.alarm-item');
                if (items.length > 15) {
                    alarmList.removeChild(items[items.length - 1]);
                }
            }

            // 更新数据状态
            const dataStatus = document.getElementById('dataStatus');
            const lastUpdate = document.getElementById('lastUpdate');
            if (dataStatus) {
                dataStatus.textContent = '数据正常';
            }
            if (lastUpdate) {
                const now = new Date();
                lastUpdate.textContent = '最后更新: ' + now.toLocaleTimeString();
            }
        }

        /**
         * 报警过滤功能
         */
        function filterAlarms(filterType) {
            const filterBtns = document.querySelectorAll('.filter-btn');
            const alarmItems = document.querySelectorAll('.alarm-item');

            // 更新按钮状态
            filterBtns.forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-filter') === filterType) {
                    btn.classList.add('active');
                }
            });

            // 过滤报警项
            alarmItems.forEach(item => {
                if (filterType === 'all') {
                    item.style.display = 'flex';
                } else {
                    if (item.classList.contains(filterType)) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                updateTime();
            }
        });

        // 定期更新报警数据
        setInterval(updateAlarmData, 5000);

        // 模拟参数数据更新
        setInterval(() => {
            // 更新参数值
            const params = [
                { id: 'load-reactive-power-value', range: [-3, -1], unit: ' MVAr' },
                { id: 'power-factor-value', range: [0.90, 0.99], unit: '' },
                { id: 'grid-reactive-current-value', range: [120, 130], unit: ' A' },
                { id: 'bus-voltage-uab-value', range: [10.4, 10.6], unit: ' kV' },
                { id: 'bus-voltage-ubc-value', range: [10.4, 10.6], unit: ' kV' },
                { id: 'bus-voltage-uca-value', range: [10.4, 10.6], unit: ' kV' },
                { id: 'svg-current-ia-value', range: [125, 135], unit: ' A' },
                { id: 'svg-current-ib-value', range: [125, 135], unit: ' A' },
                { id: 'svg-current-ic-value', range: [125, 135], unit: ' A' }
            ];

            params.forEach(param => {
                const element = document.getElementById(param.id);
                if (element) {
                    const value = (Math.random() * (param.range[1] - param.range[0]) + param.range[0]).toFixed(2);
                    element.textContent = value + param.unit;
                }
            });
        }, 3000);
    </script>
</body>
</html>
